import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.kapt)
    alias(libs.plugins.arouter)
    alias(libs.plugins.firebase)
}

android {
    namespace = "com.layaa.play"
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toInt()
        targetSdk = libs.versions.targetSdk.get().toInt()
        versionCode = 8
        versionName = "1.7.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

    }

    base.archivesName.set(
        provider {
            val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmm"))
            "layaa-v${defaultConfig.versionName}(${defaultConfig.versionCode})-${timestamp}"
        }
    )

    signingConfigs {

        create("customDebug") {
            storeFile = rootProject.file("wohoh.jks")
            storePassword = "wohoh888"
            keyPassword = "wohoh888"
            keyAlias = "wohoh"
            isV1SigningEnabled = true
            isV2SigningEnabled = true
        }

        create("customRelease") {
            storeFile = rootProject.file("layaa.jks")
            storePassword = "layaa@888"
            keyPassword = "layaa@888"
            keyAlias = "layaa"
            isV1SigningEnabled = true
            isV2SigningEnabled = true
        }
    }

    buildTypes {
        debug {
            isDebuggable = true
            signingConfig = signingConfigs.getByName("customDebug")
            manifestPlaceholders["cleartextTraffic"] = "true"
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
        release {
            isMinifyEnabled = true
            isShrinkResources = true
            manifestPlaceholders["cleartextTraffic"] = "false"
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )

        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }

    buildFeatures {
        viewBinding = true
        buildConfig = true
    }

    flavorDimensions += "default"

    productFlavors {
        create("dev") {
            applicationId = "com.biz.wohoh"
            signingConfig = signingConfigs.getByName("customDebug")
            manifestPlaceholders["app_name"] = "Wohoh"
            resValue("string", "isRelease", "false")
            resValue("string", "tencent_im_app_id", "1400812412")
            resValue("string", "zego_app_id", "1417071139")
            resValue(
                "string",
                "zego_app_sign",
                "6058dac62c9518d675f6da914924c3358704a94705958cae5268da7726149423"
            )
            ndk {
                //noinspection ChromeOsAbiSupport
                abiFilters.add("arm64-v8a")
            }
        }

        create("product") {
            applicationId = "com.layaa.play"
            signingConfig = signingConfigs.getByName("customRelease")
            manifestPlaceholders["app_name"] = "Layaa"
            resValue("string", "isRelease", "true")
            resValue("string", "tencent_im_app_id", "1400812412")
            resValue("string", "zego_app_id", "1417071139")
            resValue(
                "string",
                "zego_app_sign",
                "6058dac62c9518d675f6da914924c3358704a94705958cae5268da7726149423"
            )

            ndk {
                //noinspection ChromeOsAbiSupport
                abiFilters.addAll(listOf("armeabi-v7a", "arm64-v8a"))
            }
        }
    }

    productFlavors.all {
        manifestPlaceholders.putAll(
            mapOf(
                "FACEBOOK_APP_ID" to "FACEBOOK_APP_ID",
                "FACEBOOK_SECRET" to "FACEBOOK_SECRET",
                "GOOGLE_KEY" to "28088118606-v262prs543dfgcmgo6v1f0q46mj1vmt0.apps.googleusercontent.com",
                "APP_NAME" to "@string/app_name",
                "TWITTER_KEY" to "TWITTER_KEY",
                "TWITTER_SECRET" to "TWITTER_SECRET",
                "PUSH_PROCESS" to ":push"
            )
        )
    }

}

kapt {
    arguments {
        arg("AROUTER_MODULE_NAME", project.name)
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)

    implementation(project(":libraries:libui"))
    implementation(project(":libraries:libnet"))
    implementation(project(":libraries:skin"))
    implementation(project(":libraries:libutils"))
    implementation(project(":libraries:libwidget"))
    implementation(project(":libraries:olweb"))
    implementation(project(":libraries:payclient"))
    implementation(project(":libraries:media"))

    implementation(project(":features:mbtiapi"))
    runtimeOnly(project(":features:mbtilogic"))
    implementation(project(":features:accountapi"))
    runtimeOnly(project(":features:accountlogic"))
    implementation(project(":features:aiapi"))
    runtimeOnly(project(":features:ailogic"))
    implementation(project(":features:chatapi"))
    runtimeOnly(project(":features:chatlogic"))
    implementation(project(":features:gameapi"))
    runtimeOnly(project(":features:gamelogic"))
    implementation(project(":features:roomapi"))
    runtimeOnly(project(":features:roomlogic"))
    implementation(project(":features:shopapi"))
    runtimeOnly(project(":features:shoplogic"))
    implementation(project(":features:levelinfoapi"))
    runtimeOnly(project(":features:levelinfologic"))
    implementation(project(":features:familyapi"))
    runtimeOnly(project(":features:familylogic"))

    implementation(project(":libraries:gotoannotation"))
    implementation(project(":libraries:gotorouter"))
    kapt(project(":libraries:gotocompiler"))

    implementation(project(":libraries:encryptor"))

    annotationProcessor(libs.arouter.compiler)
    kapt(libs.arouter.compiler)
    implementation(libs.arouter.api) {
        exclude("com.android.support")
    }
    implementation(libs.arouter.annotation)

    implementation(libs.androidx.lifecycle.livedata.ktx.v262)
    implementation(libs.androidx.lifecycle.viewmodel.ktx.v262)

    implementation(libs.mmkv)
    implementation(libs.relinker)
    implementation(libs.eventbus)

    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.messaging)
}